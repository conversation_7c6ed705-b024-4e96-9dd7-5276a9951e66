#ifndef TEMP_H
#define TEMP_H
#include <rtthread.h>

// modbus.h中定义
// 标志位定义
#define FLAG1_ALMCH 0x01    // 实时告警变化标志
#define FLAG1_TIMECH 0x08   // 时间重置标志
#define FLAG1_F 0x40        // 报文组结束标志F
#define FLAG1_E 0x80        // 报文结束标志E
#define FLAG2_HISALMOV 0x01 // 历史告警溢出标志
#define FLAG2_RST 0x40      // 复位标志
#define FLAG2_HISALM 0x80   // 有未读取历史告警标志

#define DATAFLAG1 0x01
#define DATAFLAG2 0x02

#define FILE_NAME_LEN 40
#define FILE_TIME_LEN 20

// lo.h中定义
// CORE板软件接口地址
#define ADDR_INT16UBANK ((volatile rt_uint8_t *)0xF000)    // 字库起始地址
#define ADDR_NVRAM ((volatile rt_uint8_t *)0xF100)         // NVRAM起始地址
#define ADDR_DS1687 ((volatile rt_uint8_t *)0xF200)        // 时钟芯片地址
#define ADDR_SJA1000 ((volatile rt_uint8_t *)0xF300)       // CAN控制器地址
#define ADDR_16C2552 ((rt_uint8_t *)0xF400)                // 16C2552地址
#define ADDR_EXFLASH ((volatile rt_uint8_t *)0xF500)       // FLASH起始地址
#define ADDR_DEVICETYPE_L (*(volatile rt_uint8_t *)0xF700) // 系统类型拨码开关地址低8位
#define ADDR_DEVICETYPE_H (*(volatile rt_uint8_t *)0xF710) // 参数类型拨码开关地址高8位
#define ADDR_HIGH1508 (*(volatile rt_uint8_t *)0xF720)     // 16~23位地址锁存器地址
#define ADDR_HIGH2316 (*(volatile rt_uint8_t *)0xF730)     // 8~15位地址锁存器地址
#define ADDR_485CTRL (*(volatile rt_uint8_t *)0xF740)      // 485控制信号输出
#define ADDR_MEMEXP (*(volatile rt_uint8_t *)0xF800)       // 外部总线使能，MEMORY使能
#define ADDR_IOEXP (*(volatile rt_uint8_t *)0xFC00)        // I/O总线使能
//DCSIB板接口地址
#define ADDR_CS1 ((volatile rt_uint8_t *)0xFD40)
#define ADDR_CS2 ((volatile rt_uint8_t *)0xFD48)
#define ADDR_KEY (*(volatile rt_uint8_t *)0xFD50)          // 按键地址
#define ADDR_ENV (*(volatile rt_uint8_t *)0xFD58)          // 环境量地址
#define ADDR_INRELAY (*(volatile rt_uint8_t *)0xFD60)      // 输入干接点地址、直流防雷
#define ADDR_DIGIT0 (*(volatile rt_uint8_t *)0xFD68)       // 数字量0地址 1~8路负载熔丝
#define ADDR_DIGIT1 (*(volatile rt_uint8_t *)0xFD70)       // 数字量1地址 9~16路负载熔丝
#define ADDR_DIGIT2 (*(volatile rt_uint8_t *)0xFD78)       // 数字量2地址 17~24路熔丝
#define ADDR_LIGHT (*(volatile rt_uint8_t *)0xFD00)        // 系统灯地址 0：运行灯 1：通讯灯 2：液晶告警灯 3：液晶背光 // 4：液晶复位信号 5：液晶电源 6：机架告警灯 7：监控故障告警灯
#define ADDR_OUTRELAY (*(volatile rt_uint8_t *)0xFD08)     // 输出干接点地址 对应1~8路输出干接点
#define ADDR_ANALOGSELECT (*(volatile rt_uint8_t *)0xFD10) // 模拟量选通锁存地址

// 指示灯
#define RUNLED 0       // 运行灯
#define COMMLED 1      // 后台通讯灯
#define ALARMLDBLED 2  // 液晶告警灯
#define LCDLIGHT 3     // 液晶背光
#define LCDRST 4       // 液晶复位信号
#define LCDPOWER 5     // 液晶电源
#define ALARMLED 6     // 机架告警灯
#define SUALARMRELAY 7 // 监控单元故障输出干接

// 原工程sample.h中的宏定义
#define ALL_PART 0
#define DC_PART 3
#define ENV_PART 4
#define POWER_PART 6
// 默认值
#define DEFAULT_DC_VOLT 480  // 缺省直流电压  放大10倍
#define DEFAULT_BAT_VOLE 300 // 电池有效电压
#define DEFAULT_TEMP 25      // 缺省温度
#define DEFAULT_HUM 50       // 缺省湿度
#define BASE_VOLT 2.5        // 基准电压
#define ZERO_TEMP 273        // 绝对零点温度
#define MAX_TEMP 100         // 有效温度上限
#define MIN_TEMP -40         // 有效温度下限

// 原工程para.h中宏定义
#define SETTER_LOCAL 0x00  // 本地设置参数
#define SETTER_REMOTE 0x01 // 远程设置参数

// 原工程MIBTable.h中定义
#define TYPE_CHAR		0
#define TYPE_BOOLEAN	1
#define TYPE_INT8S		2
#define TYPE_INT8U		3
#define TYPE_INT16S		4
#define TYPE_INT16U		5
#define TYPE_FP32		6
#define TYPE_INT32U		7
#define TYPE_INT32S		8
#define TYPE_STRING		9
#define TYPE_STRUCT		10

// #define LEN_COMPANYNAME 20
// #define LEN_SMNAME 30
// #define LEN_VERSION 3
// #define LEN_RESERVED 13
// #define LEN_RELAYNAME 13

// ID1常量定义
#define ID1_GET_HISRECORD 0x05 // 获取历史记录ID1
#define ID1_NULL 0xff

#define ID1_INFOTYPE_MASK 0x0f // 信息类型掩码(ID1低4位)
#define ID1_ANALOG 0x00        // 模拟量ID1
#define ID1_DIGIT 0x01         // 开关量ID1
#define ID1_ALARM 0x02         // 告警量ID1
#define ID1_PARA 0x03          // 参数量ID1
#define ID1_CTRL 0x04          // 控制量ID1
#define ID1_OTHER 0x05         // 其它量ID1
#define ID1_OUTRLY 0x06        // 输出干结点量ID1
#define ID1_STATUSOUTRLY 0x07  // 状态量对应输出干结点量
#define ID1_SETSTATUS 0x08     // 可设状态量

#define ID1_DEVICETYPE_MASK 0xf0 // 设备类型掩码(ID1高4位)

#define ID1_CSU_ANALOG 0x00 // CSU模拟量ID1
#define ID1_CSU_STATUS 0x01 // CSU状态量ID1
#define ID1_CSU_ALARM 0x02  // CSU告警量ID1
#define ID1_CSU_PARA 0x03   // CSU参数量ID1
#define ID1_CSU_CTRL 0x04   // CSU控制量ID1
#define ID1_CSU_OTHER 0x05  // CSU其它量ID1
#define ID1_CSU_OUTRLY 0x06 // CSU输出干结点量ID1

#define ID1_ACMU_ANALOG 0x10 // ACMU模拟量ID1
#define ID1_ACMU_STATUS 0x11 // ACMU状态量ID1
#define ID1_ACMU_ALARM 0x12  // ACMU告警量ID1
#define ID1_ACMU_PARA 0x13   // ACMU参数量ID1
#define ID1_ACMU_CTRL 0x14   // ACMU控制量ID1
#define ID1_ACMU_OTHER 0x15  // ACMU其它量ID1
#define ID1_ACMU_OUTRLY 0x16 // ACMU告警对应输出干结点量

#define ID1_SMR_ANALOG 0x20 // SMR模拟量ID1
#define ID1_SMR_STATUS 0x21 // SMR状态量ID1
#define ID1_SMR_ALARM 0x22  // SMR告警量ID1
#define ID1_SMR_PARA 0x23   // SMR参数量ID1
#define ID1_SMR_CTRL 0x24   // SMR控制量ID1
#define ID1_SMR_OTHER 0x25  // SMR其它量ID1
#define ID1_SMR_OUTRLY 0x26 // SMR告警对应输出干结点量

#define ID1_DCMU_ANALOG 0x30    // DCMU模拟量ID1
#define ID1_DCMU_STATUS 0x31    // DCMU状态量ID1
#define ID1_DCMU_ALARM 0x32     // DCMU告警量ID1
// #define ID1_DCMU_PARA 0x33      // DCMU参数量ID1
#define ID1_DCMU_CTRL 0x34      // DCMU控制量ID1
#define ID1_DCMU_OTHER 0x35     // DCMU其它量ID1
#define ID1_DCMU_OUTRLY 0x36    // DCMU告警对应输出干结点量
#define ID1_DCMU_SETSTATUS 0x38 // DCMU可设状态量

#define ID1_ENV_ANALOG 0x40 // ENV模拟量ID1
#define ID1_ENV_STATUS 0x41 // ENV状态量ID1
#define ID1_ENV_ALARM 0x42  // ENV告警量ID1
#define ID1_ENV_PARA 0x43   // ENV参数量ID1
#define ID1_ENV_CTRL 0x44   // ENV控制量ID1
#define ID1_ENV_OTHER 0x45  // ENV其它量ID1
#define ID1_ENV_OUTRLY 0x46 // ENV告警对应输出干结点量

// 模拟量信息ID2定义
#define ID2_AN_LOADVOLT 0x01    // 负载电压
#define ID2_AN_LOADSUMCURR 0x02 // 负载总电流
#define ID2_AN_LOADCURR 0x03    // 负载电流
#define ID2_AN_BATTVOLT 0x04    // 电池电压
#define ID2_AN_BATTCURR 0x05    // 电池电流
#define ID2_AN_BATTTEMP 0x06    // 电池温度
#define ID2_AN_TOTALPOWER 0x07  // 负载总电量 //tcf
#define ID2_AN_LOADPOWER 0x08   // 负载电量//tcf
#define ID2_AN_ENVTEMP 0x01     // 环境温度
#define ID2_AN_ENVHUM 0x02      // 环境湿度

// 开关量信息ID2定义
#define ID2_DG_INRLYSTATE 0xFF // 输入干节点

// 告警量信息ID2定义
#define ID2_ALM_DCVOLTHIGH 0x01 // 直流电压高
#define ID2_ALM_DCVOLTLOW 0x02  // 直流电压低
#define ID2_ALM_DCLOOPBRK 0x03  // 负载回路断
#define ID2_ALM_SPD_DC 0x04     // 直流防雷器损坏
#define ID2_ALM_BATVLOW 0x05    // 电池电压低
#define ID2_ALM_BATVTOOLOW 0x06 // 电池电压过低
#define ID2_ALM_BATCURRFAT 0x07 // 电池电流异常
#define ID2_ALM_BATTMPHIGH 0x08 // 电池温度高
#define ID2_ALM_BATTMPLOW 0x09  // 电池温度低
#define ID2_ALM_BATLOOPBRK 0x0A // 电池回路断
#define ID2_ALM_BATDISCHG 0x0B  // 电池放电
#define ID2_ALM_BATTSENSOR 0x0C // 电池温度失效
#define ID2_ALM_RLALMTIME 0xFB  // 实时告警时间
#define ID2_ALM_HISALMTIME 0xFC // 历史告警时间
#define ID2_ALM_COMMON 0xFD     // 总告警
#define ID2_ALM_INRELAY 0xFE    // 输入干接点告警
#define ID2_ALM_ETHIGH 0x01     // 环境温度高
#define ID2_ALM_ETLOW 0x02      // 环境温度低
#define ID2_ALM_EHHIGH 0x03     // 环境湿度高
#define ID2_ALM_EHLOW 0x04      // 环境湿度低
#define ID2_ALM_SMOG 0x05       // 烟雾告警
#define ID2_ALM_FLOOD 0x06      // 水淹告警
#define ID2_ALM_DOORINTR 0x07   // 门禁告警
#define ID2_ALM_DOORMAG 0x08    // 门磁告警
#define ID2_ALM_GLASS 0x09      // 玻璃碎告警
#define ID2_ALM_ETSENSOR 0x0B   // 环境温度失效告警
#define ID2_ALM_EHSENSOR 0x0C   // 环境湿度失效告警
// 新增相关的告警参数ID
#define ID2_ALM_BATT_TEMP_Ex_OVER 0x4A // 电池温度过高
#define ID2_ALM_DC_UNDER_Ex_VOLT 0x18  // 直流电压过低
#define ID2_ALM_DC_OVER_Ex_VOLT 0x19   // 直流电压过高

// 参数量信息ID2定义
#define ID2_PR_DCOVERVOLT 0x01     // 直流过压值
#define ID2_PR_DCUNDVOLT 0x02      // 直流欠压值
#define ID2_PR_BATUNDVOLT 0x03     // 电池电压低值
#define ID2_PR_BATEXVOLT 0x04      // 电池电压过低值
#define ID2_PR_BATCURRFAT 0x05     // 电池电流异常值
#define ID2_PR_BATOVERTMP 0x06     // 电池过温值
#define ID2_PR_BATUNDTMP 0x07      // 电池低温值
#define ID2_PR_BATBRKVOLT 0x08     // 电池回路断阈值
#define ID2_PR_BATSETUP 0x09       // 电池组配置
#define ID2_PR_LOADSETUP 0x0A      // 负载配置
#define ID2_PR_DCVOFFSET 0x0C      // 直流电压零点
#define ID2_PR_DCCOFFSET 0x0D      // 负载电流零点
#define ID2_PR_DCCSLOPE 0x0E       // 负载电流斜率
#define ID2_PR_BATVOFFSET 0x0F     // 电池电压零点
#define ID2_PR_BATCOFFSET 0x10     // 电池电流零点
#define ID2_PR_BATCSLOPE 0x11      // 电池电流斜率
#define ID2_PR_BATTOFFSET 0x12     // 电池温度零点
#define ID2_PR_BATDISCURR 0x13     // 电池放电阈值
#define ID2_PR_BATTSHUNT 0x14      // 电池分流器
#define ID2_PR_LOADSHUNT 0x15      // 负载分流器
#define ID2_PR_LOADSTATESETUP 0x45 // 网元层一致负载回路状态配置//by sun2012-7-13 是否关联负载配置和告警
#define ID2_PR_INRLYALMTTL 0xF0    // 输入干结点告警电平
#define ID2_PR_INRLYNAME 0xF1      // 输入干结点名称
#define ID2_PR_REPORTINT 0xF2      // 主动上传时间间隔
#define ID2_PR_BUZZER 0xF5         // 蜂鸣器开关
#define ID2_PR_AUTOALM 0xF6        // 主动告警功能
#define ID2_PR_HISDATAINV 0xF9     // 历史数据保存时间间隔
#define ID2_PR_COMRATE 0xFB        // 串口波特率设置
#define ID2_PR_LOCALADD 0xFC       // 本机地址
#define ID2_PR_LANGUAGE 0xFD       // 语言设置
#define ID2_PR_PASSWORD 0xFE       // 菜单口令设置
#define ID2_PR_TIME 0xFF           // 系统时间设置

#define ID2_PR_ETHIGH 0x01   // 环境温度高阈值
#define ID2_PR_ETLOW 0x02    // 环境温度低阈值
#define ID2_PR_EHHIGH 0x03   // 环境湿度高阈值
#define ID2_PR_EHLOW 0x04    // 环境湿度低阈值
#define ID2_PR_ETOFFSET 0x05 // 环境温度零点
#define ID2_PR_EHOFFSET 0x06 // 环境湿度零点

// 新增相关的告警参数ID
#define ID2_PR_BATT_TEMP_Ex_OVER 0x51 // 电池温度过高
#define ID2_PR_DC_UNDER_Ex_VOLT 0x1C  // 直流电压过低
#define ID2_PR_DC_OVER_Ex_VOLT 0x1D   // 直流电压过高

//  控制量信息ID2定义
#define ID2_CTL_DOWNZK			0xEF	// 下载字库
#define ID2_CTL_RESET			0xF3	// 系统复位
#define ID2_CTL_RLYACT			0xF4	// 输出干节点打开
#define ID2_CTL_RLYRESET		0xF5	// 输出干节点关闭
#define ID2_CTL_LDRUNPARA		0xFD	// 恢复默认运行参数
#define ID2_CTL_LDCFGPARA		0xFE	// 恢复默认配置参数
#define ID2_CTL_DOWMPROG		0xFF	// 下载程序

#define ID2_DATARECORDTIME		0xF9	// 数据记录时间
#define ID2_OPER_RECORDTIME	0xFA	// 操作记录时间
#define ID2_IO_ATTRIBUTE		0xFB	// 设备IO属性
#define ID2_FACTORY_NAME		0xFC	// 厂家名称
#define ID2_SYSTEM_NAME		0xFD	// 系统名称
#define ID2_SOFTWARE_VER		0xFE	// 软件版本
#define ID2_RELEASE_DATE		0xFF	// 发布日期

#define ID2_GENERAL				0x00	// 通用ID2

#define BATCH_SEND				240	// 批量信息传送标识
#define HIS_DATA_NUM			40	//历史数据条数
#define HIS_ACTION_MSG  		8	//历史操作记录区长度


// 信息类型ID编码
#define INFO_ANALOG		0x00	// 模拟量
#define INFO_STATUS		0x01	// 状态量
#define INFO_ALARM		0x02	// 告警量
#define INFO_PARA		0x03	// 参数量
#define INFO_CTRL		0x04	// 控制量
#define INFO_OTHER		0x05	// 其他量

// 告警级别定义
#define ALARMCLASS_MASK 0     // 屏蔽
#define ALARMCLASS_CRITICAL 1 // 紧急
#define ALARMCLASS_MAJOR 2    // 重要
#define ALARMCLASS_MINOR 3    // 一般
#define ALARMCLASS_WARNING 4  // 轻微
#define ALARM_CLASS_MAX 4     // 告警级别最大值
#define ALARMCLASS_NULL 5     // 告警空，仅为比较用
// #define ALARM_CLASS_NUM 28    // 25		//告警类别总数，包括总告警

#define BATT_NUM 4        // 蓄电池组数
#define LOAD_NUM 24       // 检测负载电流路数
#define FUSE_NUM LOAD_NUM // 检测负载熔丝路数，与负载电流相同
#define INRELAY_NUM 4     // 输入干接点个数
#define OUTRELAY_NUM 8    // 输出干接点个数
#define LEN_PASS 4        // 密码长度

// 原工程common.h文件中定义
#define TRUE 1
#define FALSE 0
#define NULL 0

#define MODE_APP 0 // 应用程序
#define MODE_IAP 1 // 字库
#define	ISP_OFFSET		0xF50
#define	ISP485_OFFSET   0xEA6

#define LEN_COMM_BUF 1024

#define FLOAT_TO_INT16U(f) ((rt_uint16_t)((f) + 0.5))
#define FLOAT_TO_INT8U(f) ((rt_uint8_t)((f) + 0.5))
#define FLOAT_TO_CHAR(f) ((rt_uint8_t)((f) + 0.5))
#define FLOAT_TO_INT8S(f) (((f) >= 0) ? (rt_int16_t)((f) + 0.5) : (rt_int16_t)((f) - 0.5))
#define FLOAT_TO_INT16S(f) (((f) >= 0) ? (rt_int16_t)((f) + 0.5) : (rt_int16_t)((f) - 0.5))

//设备拨码定义
#define ZXDU88_S402			0x00
#define ZXDP12				0x04//独立销售
#define COMM_PORT_NUM 2 // 串行口数量 提供了3个，目前只使用了2个
#define COMM_PORT0 0    // 前台RS485通讯口   128芯片自带的第一个串口
#define COMM_PORT1 1    // 后台全RS232通讯口 外接16C2552

//通讯协议拨码定义
#define TYPE_PRO_HDLC		0		//HDLC协议
#define TYPE_PRO_1104		1		//1104协议
#define TYPE_PRO_485		2		//485协议 该协议不由拨码控制，是和CSU通讯的默认协议


// 按键定义
// #define KEY_BUFF_LEN 8
// #define KEY_ENTER 0xEF
// #define KEY_ESC 0xf7
// #define KEY_UP 0xFD
// #define KEY_DOWN 0xFB
// #define KEY_LEFT 0xDF
// #define KEY_RIGHT 0xFE
// #define KEY_UP_DOWN 0xF9
// #define KEY_UP_ENTER 0xED
// #define KEY_DOWN_ENTER 0xEB
// #define KEY_LERI_ENTER 0xCE
// #define KEY_NOTHING 0xFF

// 原工程alarm.h告警相关定义
/***********************  常量定义  ************************/
#define ALARM_COUNTER		7						//告警判断次数
#define FUSE_ALARM_COUNTER	(2*ALARM_COUNTER)	//告警判断次数

/* DataFlag中的告警位、开关量位 */
#define	BIT_ALARM		0
#define	BIT_SWITCH		1

#define REAL_ALARM_NUM 62			//实时告警种类
//NVRAM中增加CRC校验重启次数的保存//by  sun20120810
#define	NV_JUDGE_CRC_VAL	0	

//NVRAM中保存的实时告警的校验和地址
#define	NV_REAL_ALARM_CHECK	6	// 4+23*501=11525
//NVRAM中保存的实时告警的起始地址
#define	NV_REAL_ALARM_ADDR 	8
//NVRAM中保存的历史告警头尾指针地址
#define	NV_HIS_ALARM_INDEX	(NV_REAL_ALARM_ADDR + sizeof(T_RealAlarmStruct) * sizeof(T_AlarmStruct))
#define	NV_HIS_ALARM_ADDR	(NV_HIS_ALARM_INDEX + sizeof(T_BuffIndex))       //历史告警空间

// 原项目common.h中定义的结构体
/***************************时间数据结构  ****************************/
typedef struct
{
    rt_uint16_t wYear;   /* 年 */
    rt_uint8_t ucMonth;  /* 月 */
    rt_uint8_t ucDay;    /* 日 */
    rt_uint8_t ucHour;   /* 时 */
    rt_uint8_t ucMinute; /* 分 */
    rt_uint8_t ucSecond; /* 秒 */
} T_TimeStruct;

// 队列指针结构体
typedef struct
{
	rt_uint16_t	wWrite;
	rt_uint16_t	wRead;
	rt_uint16_t	awSendTail;
	rt_uint16_t	awAckTail;
	rt_uint16_t	wCRC;
}T_BuffIndex;

// 在线烧结FLASH字库结构体
typedef struct
{
    rt_uint8_t uFlag;
    rt_uint16_t nCounter;
    rt_uint16_t nTotal;
    rt_uint32_t lFileLength;
    char uFileName[20];
    char uFileTime[20];
    rt_uint8_t uCrcHi;
    rt_uint8_t uCrcLo;
} T_EEpromISPSruct;

/*********************	通讯底层数据的结构定义	********************/
typedef struct
{
    rt_uint8_t ucTLCF;         // 0x30：表明此帧是ASCII码编码方式,0x31H：表明此帧是16进制编码方式。
    rt_uint8_t ucProtocolFlag; // 串口对应的协议标识串口0为TYPE_PRO_485，串口1自动判断
    rt_uint16_t wMaxLen;       // 数据最大长度

    /***********  直接接收数据	**********/
    rt_uint8_t bRecReady;             /* 数据包正在接收标志（已收到包头）	*/
    rt_uint8_t bRecOk;                /* 数据包接收完毕标志 */
    rt_uint8_t ucRecTime;               /* 相邻两接收字符间隔时间，每10ms加1 */
    rt_uint8_t aucRecBuf[LEN_COMM_BUF]; /* 直接接收数据缓冲区（ASCII码，环型）*/
    rt_uint16_t wRecLength;             /* 直接接收数据缓冲区数据包长度	*/
    rt_uint16_t wRecIndex;              /* 直接接收数据缓冲区接收索引		*/
    rt_uint16_t wNoRecIntCounter;       // 没有接收中断产生累计时间

    /***********  直接发送数据	**********/
    rt_uint8_t bDataSending;           // 数据包正在发送标志
    rt_uint8_t aucSendBuf[LEN_COMM_BUF]; /* 直接发送数据缓冲区（ASCII码） */
    rt_uint16_t wSendLength;             /* 直接发送数据缓冲区数据包长度	*/
    rt_uint16_t wSendIndex;              /* 直接发送数据缓冲区发送索引	*/
    rt_uint8_t ucSendTime;               /* 相邻两发送字符间隔时间，每10ms加1 */
} T_CommStruct;

typedef struct
{
    rt_uint8_t ucMsgOVCnt;   // 系统消息溢出次数
    rt_uint8_t ucMsgInitCnt; // 重新初始化消息队列次数
    rt_uint8_t ucRS485Ctrl;  // RS485控制端口控制字
} T_SYS_DEBUG;

// 原项目中MIBTable.h中定义的结构体
// MIB库节点结构体定义
/************************************************************
** 结构名: MIBTable_ParaNode
** 描  述: 参数属性数据结构定义(表征一个参数量的属性)
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
// typedef struct
// {
//     rt_uint8_t ucID1;        // ID1
//     rt_uint8_t ucID2;        // ID2
//     rt_uint8_t ucIDNumber;   // 用于标志ID1和ID2相同的数量(如有三组电池时，电池容量ID就有3个)(当ID2=0x00时表示ID2数量)
//     rt_uint8_t ucDataType;   // 参量数据类型
//     rt_uint8_t ucDataLen;    // 参量字节长度
//     rt_int8_t scPrecision;  // 参量数据精度，小数点后几位数,如1表示小数点后1位。
//     rt_int16_t iDefaultVal; // 参量缺省值
//     rt_int16_t iMinVal;     // 参量最小值
//     rt_int16_t iMaxVal;     // 参量最大值
//     rt_int8_t scStep;       // 参量步长值
//     rt_uint8_t *ptrUnit;      // 参数单位
//     rt_uint8_t bSCAccess;  // 用于标志此参数后台能否访问,TRUE:后台可以访问,FALSE:后台不能访问
//     rt_uint8_t ucParaType;   // 用于标识此参数性质。TRUE:运行参数，所有屏统一；FALSE:配置参数，各屏独立
// } MIBTable_ParaNode;

/************************************************************
** 结构名: T_HisOperIDStruct
** 描  述: 历史操作记录ID数据结构
** 作  者: 潘奇银
** 日  期: 2008-01-15
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
// typedef struct
// {
//     rt_uint8_t ucID1;      // ID1
//     rt_uint8_t ucID2;      // ID2
//     rt_uint8_t ucIDNumber; // ID1和ID2相同的数量(如有三个整流器时，整流器在位信息ID就有3个),另当ID2=0x00时，表示ID2总数
// } MIB_HisOperIDNode;

/************************************************************
** 结构名: MIB_AnalogDataNode
** 描  述: 模拟量属性数据结构定义(表征一个模拟量的属性)
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
// typedef struct
// {
//     rt_uint8_t ucID1;       // ID1
//     rt_uint8_t ucID2;       // ID2
//     rt_uint8_t ucIDNumber;  // ID1和ID2相同的数量(例如负载有24路，那么这个数就是24)
//     rt_uint8_t ucDataType;  // 参量数据类型
//     rt_uint8_t ucDataLen;   // 参量字节长度
//     rt_int8_t scPrecision; // 参量数据精度，小数点后几位数,如1表示小数点后1位。
// } MIB_AnalogDataNode;

/************************************************************
** 结构名: MIB_DigitalDataNode
** 描  述: 数字量属性数据结构定义(表征一个数字量的属性)
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
// typedef struct
// {
//     rt_uint8_t ucID1;      // ID1
//     rt_uint8_t ucID2;      // ID2
//     rt_uint8_t ucIDNumber; // ID1和ID2相同的数量(例如有24路负载，该数字就是24),另当ID2=0x00时，表示ID2总数
// } MIB_DigitalDataNode;

/************************************************************
** 结构名: MIB_CtrlDataNode
** 描  述: 控制量属性数据结构定义(表征一个数字量的属性)
** 作  者: 刘东波
** 日  期: 2008-10-11
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
// typedef struct
// {
//     rt_uint8_t ucID1;      // ID1
//     rt_uint8_t ucID2;      // ID2
//     rt_uint8_t ucIDNumber; // ID1和ID2相同的数量(例如有24路负载，该数字就是24),另当ID2=0x00时，表示ID2总数
// } MIB_CtrlDataNode;

/************************************************************
** 结构名: T_SysPara_old
** 描  述: 系统参数数据结构定义
** 作  者: 刘东波
** 日  期: 2008-09-11
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
// typedef struct
// {
//     // DCMU参数
//     rt_int16_t iDcVolMax;                 // 直流电压高阈值
//     rt_int16_t iDcVolMin;                 // 直流电压低阈值
//     rt_int16_t iBattVolLow;               // 电池电压低阈值
//     rt_int16_t iBattVolTooLow;            // 电池电压过低阈值
//     rt_uint8_t ucBattCurrFault;           // 电池电流异常阈值
//     rt_int8_t scBattTempMax;              // 电池温度高阈值
//     rt_int8_t scBattTempMin;              // 电池温度低阈值
//     rt_int16_t iFuseValveVol;             // 电池回路断告警阈值
//     rt_uint8_t bBattSetup[BATT_NUM];      // 电池组配置
//     rt_uint8_t bLoadSetup[LOAD_NUM];      // 负载配置
//     rt_uint8_t bLoadStateSetup;           // 负载回路状态配置
//     rt_int16_t iDcVoltZero;               // 直流输出电压零点
//     rt_int16_t iDcCurrZero[LOAD_NUM];     // 负载电流零点
//     rt_int16_t iDcCurrSlope[LOAD_NUM];    // 负载电流斜率
//     rt_int16_t aiBattVoltZero[BATT_NUM];  // 电池电压零点
//     rt_int16_t aiBattCurrZero[BATT_NUM];  // 电池电流零点
//     rt_int16_t aiBattCurrSlope[BATT_NUM]; // 电池电流斜率
//     rt_int8_t ascBattTempZero[BATT_NUM];  // 电池温度零点
//     rt_int16_t aiBattDisCharge;           // 电池放电阈值
//     rt_int16_t aiBatShunt[BATT_NUM];      // 电池分流器
//     rt_int16_t aiLoadShunt[LOAD_NUM];     // 负载分流器
//     // 新增3种告警参数阈值
//     rt_int8_t scBatTempExMax;  // 电池温度过高阈值
//     rt_int16_t aiDcVoltExLow;  // 直流电压过低阈值
//     rt_int16_t aiDcVoltExHigh; // 直流电压过高阈值

//     rt_uint16_t awAutoReport[COMM_PORT_NUM];          // 主动告警时间间隔
//     rt_uint8_t aucRlyAlarmTTL[INRELAY_NUM];           // 输入干结点告警电平
//     rt_uint8_t aucRlyAlarmGrade[INRELAY_NUM];         // 输入干结点告警级别
//     rt_uint8_t acRlyName[INRELAY_NUM][LEN_RELAYNAME]; // 输入干结点名称
//     rt_uint8_t aucAlmGrade[ALARM_CLASS_NUM];          // 告警级别
//     rt_uint8_t aucAlmOutRly[ALARM_CLASS_NUM];         // 告警输出干结点
//     rt_uint8_t bBeepOn;                               // 蜂鸣器开关 TRUE 为开 FALSE为关
//     rt_uint8_t bAutoAlarm;                            // 非MODEM组网方式主动告警功能使能 TRUE允许　 FALSE禁止
//     rt_uint8_t ucHisDataInterval;                     // 历史数据保存间隔时间
//     rt_uint8_t ucRS232Bps[COMM_PORT_NUM];             // RS232波特率
//     rt_uint16_t wDeviceAddr[COMM_PORT_NUM];           // 设备地址编号	***
//     rt_uint8_t ucLanguage;                            // 语言版本
//     rt_uint8_t acPaserword[LEN_PASS];                  // 操作口令

//     // ENV参数
//     rt_int8_t scEnvTempMax;  // 环境温度高告警值
//     rt_int8_t scEnvTempMin;  // 环境温度低告警值
//     rt_int8_t scEnvHumMax;   // 环境湿度高告警值
//     rt_int8_t scEnvHumMin;   // 环境湿度低告警值
//     rt_int8_t scEnvTempZero; // 环境温度零点
//     rt_int8_t scEnvHumZero;  // 环境湿度零点
//     // Ext参数
//     rt_uint8_t aucHostAdd[COMM_PORT_NUM]; // 主机号

// } T_SysPara_old;  //gui_data_interface.h中已经重定义T_SysPara

typedef struct
{
    rt_uint16_t wWrite;
    rt_uint16_t wRead;
    rt_uint16_t awSendTail;
    rt_uint16_t awAckTail;
    rt_uint16_t wCRC;
} T_HisEventBUFFIndex;

/************************************************************
** 结构名: T_RealAlarmStruct
** 描  述: 实时告警数据结构
** 作  者: 潘奇银
** 日  期: 2008-01-09
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
// typedef struct
// {
//     rt_uint8_t ucAlarmSn;         // 告警序号
//     rt_uint8_t ucIDIndex;         // 告警设备索引(如SMR1/2/3)
//     rt_uint8_t ucAlarmState;      // 告警级别
//     T_TimeStruct tStartTime; // 告警开始时间
// } T_RealAlarmStruct;

/************************************************************
** 结构名: T_DCAlarmStruct
** 描  述: DC段告警数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-09
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
typedef struct
{
    rt_uint8_t ucVoutHigh;                  // 直流输出电压高
    rt_uint8_t ucVoutLow;                   // 直流输出电压低
    rt_uint8_t aucLoadLoop[FUSE_NUM];       // 负载回路断
    rt_uint8_t ucSPD_DC;                    // 直流避雷器状态异常
    rt_uint8_t aucBattVoltLow[BATT_NUM];    // 电池电压低
    rt_uint8_t aucBattVolttooLow[BATT_NUM]; // 电池电压过低
    rt_uint8_t aucBattCurrFault[BATT_NUM];  // 电池电流异常
    rt_uint8_t aucBattTempHigh[BATT_NUM];   // 电池温度高
    rt_uint8_t aucBattTempLow[BATT_NUM];    // 电池温度低
    rt_uint8_t aucBattLoop[BATT_NUM];       // 电池回路断
    rt_uint8_t aucBattDischarge[BATT_NUM];  // 电池放电
    rt_uint8_t aucBattTSensor[BATT_NUM];    // 电池温度失效
    rt_uint8_t aslBatTempExHigh[BATT_NUM];  // 电池温度过高
    rt_uint8_t slDcVolExtraLow;             // 直流电压过低
    rt_uint8_t slDcVolExtraHigh;            // 直流电压过高
} T_DCAlarmStruct;

/************************************************************
** 结构名: T_ENVAlarmStruct
** 描  述: ENV段告警数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-09
** 版  本: V5.0
** 修改记录
** 日  期		版	本		修改人		修改摘要
**
**************************************************************/
typedef struct
{
    rt_uint8_t ucTempHigh;    // 环境温度高
    rt_uint8_t ucTempLow;     // 环境温度低
    rt_uint8_t ucHumHigh;     // 环境湿度高
    rt_uint8_t ucHumLow;      // 环境湿度低
    rt_uint8_t ucSmog;        // 烟雾告警
    rt_uint8_t ucFlood;       // 水淹告警
    rt_uint8_t ucIntrusion;   // 门禁告警
    rt_uint8_t ucDoorMag;     // 门磁告警
    rt_uint8_t ucGlassBroken; // 玻璃碎告警
    rt_uint8_t ucETSensor;    // 环境温度失效
    rt_uint8_t ucEHSensor;    // 环境湿度失效
} T_ENVAlarmStruct;

/************************************************************
** 结构名: T_AlarmStruct
** 描  述: 告警数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-09
** 版  本: V5.0 
** 修改记录			
** 日  期		版	本		修改人		修改摘要
** 
**************************************************************/
typedef struct
{
	rt_uint8_t ucCommon;					//总告警
	T_DCAlarmStruct	tDCAlarm;
	T_ENVAlarmStruct	tENVAlarm;	
	rt_uint8_t 	aucInRelay[INRELAY_NUM];		// 输入干结点告警
}T_AlarmStruct;

// // 厂家信息结构体定义
// typedef struct
// {
//     char acCompanyName[LEN_COMPANYNAME + 1]; // 厂家名称
//     char acSMName[LEN_SMNAME + 1];           // SM名称
//     rt_uint8_t aucVersion[4];                     // SM软件版本
//     rt_uint16_t wReleaseYear;                     // SM软件发布日期:年
//     rt_uint8_t ucReleaseMonth;                    // SM软件发布日期:月
//     rt_uint8_t ucReleaseDay;                      // SM软件发布日期:日
//     char acReserved[LEN_RESERVED];           // 预留
// } T_FactoryInfo;

// 电量数据结构体
typedef struct
{
    float fTotalPower;          // 总电量
    float fLoadPower[LOAD_NUM]; // 分路电量
} T_DcPowerDataStruct;

typedef struct
{
    float fVout;           // 直流输出电压浮点数用于电量计算
    float fLoadTotalIout;  // 负载电流浮点数用于电量计算
    float fIout[LOAD_NUM]; // 分路负载电流浮点数用于电量计算

} T_DcAnalogVIFloatStruct;

typedef struct
{
    T_DcPowerDataStruct tPowerData; // 电量数据  //tcf 20160622
    T_DcAnalogVIFloatStruct tVIFloatData;
} T_PowerDataStruct;

// 通讯模块comm.h相关结构体
/*********************  数据结构定义  **********************/       
typedef struct
{
    rt_uint16_t awRxNum[COMM_PORT_NUM]; // 串口1、2接收中断计数器
    rt_uint16_t awTxNum[COMM_PORT_NUM]; // 串口1、2发送中断计数器
    rt_uint16_t awRecPtkNum[COMM_PORT_NUM];  // 串口1、2接收完整数据包数量
    rt_uint16_t awSendPtkNum[COMM_PORT_NUM]; // 串口1、2发送完整数据包数量
    rt_uint16_t awChkPtkNum[COMM_PORT_NUM]; // 串口1、2通过校验数据包数量
    rt_uint16_t awEnTxNum[COMM_PORT_NUM];   // 串口1、2启动发送中断数量
    rt_uint16_t awProtocolFlag[COMM_PORT_NUM]; // 当前通讯的协议类型
    rt_uint16_t awRecLen[COMM_PORT_NUM]; // by sun 2012-7-17增加调测信息
    rt_uint16_t awRecStep[COMM_PORT_NUM];
    rt_uint16_t awRecChk[COMM_PORT_NUM];
    rt_uint16_t awCalChk[COMM_PORT_NUM];
    rt_uint16_t awSendLen[COMM_PORT_NUM];
    rt_uint16_t awSendStep[COMM_PORT_NUM];
} T_COMM_DEBUG;

// modbus.h中结构体
typedef struct
{
    rt_uint16_t usDataLenPerFrame;
    rt_uint16_t usTotalFrameNum;
    rt_uint32_t ulTotalFileLength;
    char acFileName[FILE_NAME_LEN];
    char acFileTime[FILE_TIME_LEN];
    rt_uint16_t usFileCheck;
    rt_uint16_t usResv;
} T_FileAttrStruct;

typedef struct
{
    rt_uint8_t ucFlag;
    rt_uint16_t usCounter;
    T_FileAttrStruct tFileAttr;
    rt_uint8_t uCrcHi;
    rt_uint8_t uCrcLo;
} T_FileManageStruct;

extern rt_uint8_t ucDeviceType;
extern rt_uint8_t g_ucDownloadMode;
extern rt_uint8_t g_abNeedSendAlarm[COMM_PORT_NUM];
extern rt_uint8_t g_abAutoSendAlarmFlag[COMM_PORT_NUM]; // 1104处于主动告警流程标志 更改为数组形式 by wu 2010.3.11
extern rt_uint8_t g_bHisAlarmChk;
extern rt_uint16_t g_wMaxHisEventLen;                   // 可保存事件记录条数
extern rt_uint16_t g_wMaxHisAlmLen;                     // 可保存历史告警条数
extern rt_uint8_t s_ucAutoSendTimes;
extern rt_uint8_t s_ucAutoSendStep;
extern rt_uint16_t s_wAutoSendWaitTmr;
extern rt_uint16_t s_wCmdNum[9];
extern T_EEpromISPSruct tPrevISP;
extern T_CommStruct g_atComm[COMM_PORT_NUM];
extern T_SYS_DEBUG g_tSysDebug;
extern T_COMM_DEBUG g_tCommDebug;
extern T_FileManageStruct s_tFileManage;
#endif