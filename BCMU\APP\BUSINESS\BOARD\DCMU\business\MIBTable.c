/*
 * @Author: dml00265523
 * @Date: 2025-06-13 10:35:36
 * @LastEditTime: 2025-07-17 09:07:49
 * @Description: 数据属性定义文件
 * @FilePath: \BCMU\APP\BUSINESS\BOARD\ACMU\business\MIBTable.c
 */
 #include "MIBTable.h"
 #include "const_define_in.h"
 #include "para_id_in.h"
 #include "type_define_in.h"
 #include "alarm_id_in.h"
 #include "dev_dcmu.h"
 #include "data_type.h"
 #include "para_manage.h"
 #include "his_record.h"
 
MIBTable_ParaNode Para_MIBTable[] =
{
    // 注意:下面每一行位置固定，只能在最后新增，如果要删除或者插入，对应string.txt和T_syspara都要修改
    // 参数量
    {DCMU_PARA_ID_DC_VOLTAGE_HIGH_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_DCOVERVOLT, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_DC_VOLTAGE_HIGH_THRESHOLD, 1, "V"},            // 直流电压高阈值
    {DCMU_PARA_ID_DC_VOLTAGE_LOW_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_DCUNDVOLT, 1, TYPE_INT16S, 2, PARATYPE_RUNNING,DCMU_PARA_ID_DC_VOLTAGE_LOW_THRESHOLD, 1, "V"},            // 直流电压低阈值
    {DCMU_PARA_ID_BATTERY_VOLTAGE_LOW_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_BATUNDVOLT, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_VOLTAGE_LOW_THRESHOLD, 1, "V"},            // 电池电压低阈值
    {DCMU_PARA_ID_BATTERY_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_BATEXVOLT, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_VOLTAGE_TOO_LOW_THRESHOLD, 1, "V"},            //电池电压过低阈值
    {DCMU_PARA_ID_BATTERY_CURRENT_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_BATCURRFAT, 1, TYPE_INT8U, 1, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_CURRENT_THRESHOLD, 1, ""},            //电池电流异常比率阈值
    {DCMU_PARA_ID_BATTERY_TEMPERATURE_HIGH_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_BATOVERTMP, 1, TYPE_INT8S, 1, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_TEMPERATURE_HIGH_THRESHOLD, 1, "℃"},            // 电池温度高阈值
    {DCMU_PARA_ID_BATTERY_TEMPERATURE_LOW_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_BATUNDTMP, 1, TYPE_INT8S, 1, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_TEMPERATURE_LOW_THRESHOLD, 1, "℃"},            // 电池温度低阈值
    {DCMU_PARA_ID_BATTERY_LOOP_BROKEN_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_BATBRKVOLT, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_LOOP_BROKEN_THRESHOLD, 1, "V"},                // 电池回路断阈值
    {DCMU_PARA_ID_BATTERY_CONFIG_OFFSET, ID1_DCMU_PARA, ID2_PR_BATSETUP, BATT_NUM, TYPE_INT8S, 1, PARATYPE_CONFIG, DCMU_PARA_ID_BATTERY_CONFIG, 1, ""},            //电池组配置
    {DCMU_PARA_ID_LOAD_CONFIG_OFFSET, ID1_DCMU_PARA, ID2_PR_LOADSETUP, LOAD_NUM, TYPE_INT8S, 1, PARATYPE_CONFIG, DCMU_PARA_ID_LOAD_CONFIG, 1, ""},        //负载配置
    {DCMU_PARA_ID_LOAD_LOOP_CONFIG_OFFSET, ID1_DCMU_PARA, ID2_PR_LOADSTATESETUP, 1, TYPE_INT8S, 1, PARATYPE_CONFIG, DCMU_PARA_ID_LOAD_LOOP_CONFIG, 1, ""},           //负载回路状态配置
    {DCMU_PARA_ID_DC_VOLT_ZERO_OFFSET, ID1_DCMU_PARA, ID2_PR_DCVOFFSET, 1, TYPE_INT16S, 2, PARATYPE_CONFIG, DCMU_PARA_ID_DC_VOLT_ZERO, 1, ""},                // 直流电压零点
    {DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET, ID1_DCMU_PARA, ID2_PR_DCCOFFSET, LOAD_NUM, TYPE_INT16S, 2,PARATYPE_CONFIG, DCMU_PARA_ID_LOAD_CURRENT_ZERO, 1, ""},        // 负载电流零点
    {DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET, ID1_DCMU_PARA, ID2_PR_DCCSLOPE, LOAD_NUM, TYPE_INT16S, 2, PARATYPE_CONFIG, DCMU_PARA_ID_LOAD_CURRENT_SLOPE, 1, ""},        // 负载电流斜率
    {DCMU_PARA_ID_BATTERY_VOLT_ZERO_OFFSET, ID1_DCMU_PARA, ID2_PR_BATVOFFSET, BATT_NUM, TYPE_INT16S, 2, PARATYPE_CONFIG, DCMU_PARA_ID_BATTERY_VOLT_ZERO, 1, ""},        //电池电压零点
    {DCMU_PARA_ID_BATTERY_CURRENT_ZERO_OFFSET, ID1_DCMU_PARA, ID2_PR_BATCOFFSET, BATT_NUM, TYPE_INT16S, 2, PARATYPE_CONFIG, DCMU_PARA_ID_BATTERY_CURRENT_ZERO, 1, ""},    // 电池电流零点
    {DCMU_PARA_ID_BATTERY_CURRENT_SLOPE_OFFSET, ID1_DCMU_PARA, ID2_PR_BATCSLOPE, BATT_NUM, TYPE_INT16S, 2, PARATYPE_CONFIG, DCMU_PARA_ID_BATTERY_CURRENT_SLOPE, 1, ""},        // 电池电流斜率
    {DCMU_PARA_ID_BATTERY_TEMP_ZERO_OFFSET, ID1_DCMU_PARA, ID2_PR_BATTOFFSET, BATT_NUM, TYPE_INT8S, 1, PARATYPE_CONFIG, DCMU_PARA_ID_BATTERY_TEMP_ZERO, 1, ""},        // 电池温度零点
    {DCMU_PARA_ID_BATTERY_DISCHARGE_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_BATDISCURR, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_DISCHARGE_THRESHOLD, 10, "A"},        //电池放电阈值
    {DCMU_PARA_ID_BATTERY_TEMPERATURE_TOO_HIGH_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_BATT_TEMP_Ex_OVER, 1, TYPE_INT8S, 1, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_TEMPERATURE_TOO_HIGH_THRESHOLD, 1, "℃"},            // 电池温度高阈值
    {DCMU_PARA_ID_DC_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_DC_UNDER_Ex_VOLT, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_DC_VOLTAGE_TOO_LOW_THRESHOLD, 1, "V"},
    {DCMU_PARA_ID_DC_VOLTAGE_TOO_HIGH_THRESHOLD_OFFSET, ID1_DCMU_PARA, ID2_PR_DC_OVER_Ex_VOLT, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_DC_VOLTAGE_TOO_HIGH_THRESHOLD, 1, "V"},
    {DCMU_PARA_ID_INRELAYTTL_OFFSET, ID1_DCMU_PARA, ID2_PR_INRLYALMTTL, IN_RELAY_NUM, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_PARA_ID_INRELAYTTL, 1, ""},            // 输入干节点告警电平
    {DCMU_PARA_ID_INRELAYLEVEL_OFFSET, ID1_DCMU_PARA, ID2_PR_INRLYALMLEVEL, IN_RELAY_NUM, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_PARA_ID_INRELAYLEVEL, 1, ""},            // 输入干节点告警级别
    {DCMU_PARA_ID_INRELAYNAME_OFFSET,ID1_DCMU_PARA, ID2_PR_INRLYNAME, IN_RELAY_NUM, TYPE_STRING, LEN_RELAYNAME, PARATYPE_CONFIG, DCMU_PARA_ID_INRELAYNAME, 1, ""},            // 输入干节点名称
    {DCMU_PARA_ID_BUZZER_SWITCH_OFFSET, ID1_DCMU_PARA, ID2_PR_BUZZER, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_PARA_ID_BUZZER_SWITCH, 1, ""},        // 蜂鸣器开关
    {DCMU_PARA_ID_HISTORY_SAVE_INTERVAL_OFFSET, ID1_DCMU_PARA, ID2_PR_HISDATAINV, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_PARA_ID_HISTORY_SAVE_INTERVAL, 1, "H"},                // 历史数据保存时间间隔
    {DCMU_PARA_ID_UART_BAUDRATE_OFFSET, ID1_DCMU_PARA, ID2_PR_COMRATE, COMM_PORT_NUM, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_PARA_ID_UART_BAUDRATE, 1,""},        // 串口波特率设置
    {DCMU_PARA_ID_DEVICE_ADDR_OFFSET, ID1_DCMU_PARA, ID2_PR_LOCALADD, 1, TYPE_INT16U, 2, PARATYPE_CONFIG, DCMU_PARA_ID_DEVICE_ADDR, 1,""},    // 本机地址
    {DCMU_PARA_ID_LANGUAGE_SET_OFFSET, ID1_DCMU_PARA, ID2_PR_LANGUAGE, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_PARA_ID_LANGUAGE_SET, 1,""},                    // 语言设置
    {DCMU_PARA_ID_MENU_PASERWORD_OFFSET, ID1_DCMU_PARA, ID2_PR_PASERWORD, 1, TYPE_STRING, LEN_PASER, PARATYPE_CONFIG, DCMU_PARA_ID_MENU_PASERWORD, 1,""},        // 菜单口令设置
    {DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_UPPER_LIMIT_OFFSET, ID1_ENV_PARA, ID2_PR_ETHIGH, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_UPPER_LIMIT, 1, "℃"},  // 环境温度高阈值
    {DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_LOWER_LIMIT_OFFSET, ID1_ENV_PARA, ID2_PR_ETLOW, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_LOWER_LIMIT, 1, "℃"},   // 环境温度低阈值
    {DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_UPPER_LIMIT_OFFSET, ID1_ENV_PARA, ID2_PR_EHHIGH, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_UPPER_LIMIT, 1, "%"},      // 环境湿度高阈值
    {DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_LOWER_LIMIT_OFFSET, ID1_ENV_PARA, ID2_PR_EHLOW, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_LOWER_LIMIT, 1, "%"},        // 环境湿度低值
    {DCMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET, ID1_ENV_PARA, ID2_PR_ETOFFSET, 1, TYPE_INT8S, 1, PARATYPE_CONFIG, DCMU_PARA_ID_ENV_TEMP_ZERO_POINT, 1, ""},                    // 环境温度零点
    {DCMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET, ID1_ENV_PARA, ID2_PR_EHOFFSET, 1, TYPE_INT8S, 1, PARATYPE_CONFIG, DCMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT, 1, ""},                    // 环境湿度零点
    {DCMU_PARA_ID_BATTERY_SHUNT_OFFSET, ID1_DCMU_PARA, ID2_PR_BATTSHUNT, BATT_NUM, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_BATTERY_SHUNT, 100, "A/25mV"},        //电池分流器
    {DCMU_PARA_ID_LOAD_SHUNT_OFFSET, ID1_DCMU_PARA, ID2_PR_LOADSHUNT, LOAD_NUM, TYPE_INT16S, 2, PARATYPE_RUNNING, DCMU_PARA_ID_LOAD_SHUNT, 100, "A/25mV"},        //负载分流器
    {DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, ID1_DCMU_PARA, ID2_PR_USAGESCENARIO, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_PARA_ID_USAGE_SCENARIO, 1, ""},    // 使用场景

    //告警级别，总共16+11+1
    //告警级别 16
    {DCMU_ALM_ID_TOTAL_ALARM_LEVEL, ID1_DCMU_ALARM, ID2_ALM_COMMON, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_TOTAL_ALARM_LEVEL },        // 总告警
    {DCMU_ALM_ID_DC_VOLT_HIGH_LEVEL, ID1_DCMU_ALARM, ID2_ALM_DCVOLTHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_VOLT_HIGH_LEVEL },    // 直流电压高 
    {DCMU_ALM_ID_DC_VOLT_LOW_LEVEL, ID1_DCMU_ALARM, ID2_ALM_DCVOLTLOW,1 , TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_VOLT_LOW_LEVEL },    // 直流电压低
    {DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_LEVEL, ID1_DCMU_ALARM, ID2_ALM_DCLOOPBRK, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_LEVEL },    // 直流负载回路断
    {DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_LEVEL, ID1_DCMU_ALARM, ID2_ALM_SPD_DC, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_LEVEL },        // 直流防雷器损坏
    {DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATVLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_LEVEL },        // 电池电压低
    {DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATVTOOLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_LEVEL },    // 电池电压过低
    {DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATCURRFAT, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_LEVEL },    // 电池电流异常
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATTMPHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_LEVEL },    // 电池温度高
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATTMPLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_LEVEL },    // 电池温度低
    {DCMU_ALM_ID_BATTERY_LOOP_BROKEN_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATLOOPBRK, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_LOOP_BROKEN_LEVEL },    // 电池回路断
    {DCMU_ALM_ID_BATTERY_DISCHARGE_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATDISCHG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_DISCHARGE_LEVEL },        // 电池放电
    {DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATTSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_LEVEL },// 电池温度失效
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_LEVEL, ID1_DCMU_ALARM, ID2_ALM_BATT_TEMP_Ex_OVER, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_LEVEL },    // 电池温度过高
    {DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_LEVEL, ID1_DCMU_ALARM, ID2_ALM_DC_UNDER_Ex_VOLT, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_LEVEL },        // 直流电压过低
    {DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_LEVEL, ID1_DCMU_ALARM, ID2_ALM_DC_OVER_Ex_VOLT, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_LEVEL },// 直流电压过高
    //ENV告警级别 11
    {DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_ETHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM_LEVEL },    // 环境温度高
    {DCMU_ALM_ID_ENV_TEMP_LOW_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_ETLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_TEMP_LOW_ALARM_LEVEL },    // 环境温度低
    {DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_EHHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_LEVEL },    // 环境湿度高
    {DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_EHLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_LEVEL },    // 环境湿度低
    {DCMU_ALM_ID_FUMES_SENSOR_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_SMOG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_FUMES_SENSOR_ALARM_LEVEL },    // 烟雾告警
    {DCMU_ALM_ID_FLOOD_SENSOR_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_FLOOD, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_FLOOD_SENSOR_ALARM_LEVEL },    // 水淹告警
    {DCMU_ALM_ID_DOORMAT_SENSOR_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_DOORMAG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DOORMAT_SENSOR_ALARM_LEVEL },    // 门磁告警
    {DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_ETSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_LEVEL },    // 环境温度失效告警
    {DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_EHSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_LEVEL },    // 环境湿度失效告警 
    //对应输入干接点告警
    {DCMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL, ID1_DCMU_ALARM, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL },
    {DCMU_ALM_ID_INPUT_RELAY2_ALARM_LEVEL, ID1_DCMU_ALARM, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INPUT_RELAY2_ALARM_LEVEL },  
    {DCMU_ALM_ID_INPUT_RELAY3_ALARM_LEVEL, ID1_DCMU_ALARM, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INPUT_RELAY3_ALARM_LEVEL },  
    {DCMU_ALM_ID_INPUT_RELAY4_ALARM_LEVEL, ID1_DCMU_ALARM, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INPUT_RELAY4_ALARM_LEVEL },  

    //告警干接点，总共13+11+1+3
    //告警干接点 13
    {DCMU_ALM_ID_TOTAL_ALARM_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_COMMON, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_TOTAL_ALARM_RELAY },        // 总告警
    {DCMU_ALM_ID_DC_VOLT_HIGH_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_DCVOLTHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_VOLT_HIGH_RELAY },        // 直流电压高 
    {DCMU_ALM_ID_DC_VOLT_LOW_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_DCVOLTLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_VOLT_LOW_RELAY },        // 直流电压低
    {DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_DCLOOPBRK, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_RELAY },        // 直流负载回路断
    {DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_SPD_DC, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_RELAY },            // 直流防雷器损坏
    {DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATVLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_RELAY },        // 电池电压低
    {DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATVTOOLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_RELAY },    // 电池电压过低
    {DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATCURRFAT, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_RELAY },        // 电池电流异常
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATTMPHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_RELAY },        // 电池温度高
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATTMPLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_RELAY },        // 电池温度低
    {DCMU_ALM_ID_BATTERY_LOOP_BROKEN_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATLOOPBRK, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_LOOP_BROKEN_RELAY },        // 电池回路断
    {DCMU_ALM_ID_BATTERY_DISCHARGE_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATDISCHG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_DISCHARGE_RELAY },        // 电池放电
    {DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATTSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_RELAY },        // 电池温度失效
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_BATT_TEMP_Ex_OVER, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_RELAY },        // 电池温度过高
    {DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_DC_UNDER_Ex_VOLT, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_RELAY },        // 直流电压过低
    {DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_DC_OVER_Ex_VOLT, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_RELAY },        // 直流电压过高 

    // ENV告警对应输出干结点 11
    {DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_ETHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM_RELAY },            // 环境温度高
    {DCMU_ALM_ID_ENV_TEMP_LOW_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_ETLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_TEMP_LOW_ALARM_RELAY },            // 环境温度低
    {DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_EHHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_RELAY },            // 环境湿度高
    {DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_EHLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_RELAY },            // 环境湿度低
    {DCMU_ALM_ID_FUMES_SENSOR_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_SMOG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_FUMES_SENSOR_ALARM_RELAY },            // 烟雾告警
    {DCMU_ALM_ID_FLOOD_SENSOR_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_FLOOD, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_FLOOD_SENSOR_ALARM_RELAY },            // 水淹告警
    {DCMU_ALM_ID_DOORMAT_SENSOR_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_DOORMAG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_DOORMAT_SENSOR_ALARM_RELAY },            // 门磁告警
    {DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_ETSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_RELAY },        // 环境温度失效告警
    {DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_EHSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_RELAY },        // 环境湿度失效告警 
    //对应输入干接点告警
    {DCMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY },
    {DCMU_ALM_ID_INPUT_RELAY2_ALARM_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INPUT_RELAY2_ALARM_RELAY },  
    {DCMU_ALM_ID_INPUT_RELAY3_ALARM_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INPUT_RELAY3_ALARM_RELAY },  
    {DCMU_ALM_ID_INPUT_RELAY4_ALARM_RELAY, ID1_DCMU_OUTRLY, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, DCMU_ALM_ID_INPUT_RELAY4_ALARM_RELAY },  

    {0xFFFF, 0xFF,0xFF, 0, 0, 0, PARATYPE_ALL, 0xFFFF, 0, "" },            // IDNumber为0表明数组结束
};

const alarm_map_t alarm_map[]=
{
    // 注意:实时告警数据结构要以这个表格的顺序来定义
    // DCMU告警量
    {DCMU_ALM_ID_TOTAL_ALARM, ID1_DCMU_ALARM, ID2_ALM_COMMON, 1},    // 总告警
    {DCMU_ALM_ID_DC_VOLT_HIGH, ID1_DCMU_ALARM, ID2_ALM_DCVOLTHIGH, 1},                // 直流电压高
    {DCMU_ALM_ID_DC_VOLT_LOW, ID1_DCMU_ALARM, ID2_ALM_DCVOLTLOW, 1},                // 直流电压低
    {DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN, ID1_DCMU_ALARM, ID2_ALM_DCLOOPBRK, FUSE_NUM},        // 直流负载回路断
    {DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED, ID1_DCMU_ALARM, ID2_ALM_SPD_DC, 1},                    // 直流防雷器损坏
    {DCMU_ALM_ID_BATTERY_VOLTAGE_LOW, ID1_DCMU_ALARM, ID2_ALM_BATVLOW, BATT_NUM},            // 电池电压低
    {DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW, ID1_DCMU_ALARM, ID2_ALM_BATVTOOLOW, BATT_NUM},        // 电池电压过低
    {DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT, ID1_DCMU_ALARM, ID2_ALM_BATCURRFAT, BATT_NUM},        // 电池电流异常
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH, ID1_DCMU_ALARM, ID2_ALM_BATTMPHIGH, BATT_NUM},        // 电池温度高
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW, ID1_DCMU_ALARM, ID2_ALM_BATTMPLOW, BATT_NUM},        // 电池温度低
    {DCMU_ALM_ID_BATTERY_LOOP_BROKEN, ID1_DCMU_ALARM, ID2_ALM_BATLOOPBRK, BATT_NUM},        // 电池回路断
    {DCMU_ALM_ID_BATTERY_DISCHARGE, ID1_DCMU_ALARM, ID2_ALM_BATDISCHG, BATT_NUM},        // 电池放电
    {DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE, ID1_DCMU_ALARM, ID2_ALM_BATTSENSOR, BATT_NUM},        // 电池温度失效
    //新增告警
    {DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH, ID1_DCMU_ALARM, ID2_ALM_BATT_TEMP_Ex_OVER, BATT_NUM},        // 电池温度过高
    {DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW, ID1_DCMU_ALARM,  ID2_ALM_DC_UNDER_Ex_VOLT, 1},
    {DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH, ID1_DCMU_ALARM, ID2_ALM_DC_OVER_Ex_VOLT, 1},

    // ENV告警量
    {DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM, ID1_ENV_ALARM, ID2_ALM_ETHIGH, 1},        // 环境温度高
    {DCMU_ALM_ID_ENV_TEMP_LOW_ALARM, ID1_ENV_ALARM, ID2_ALM_ETLOW, 1},        // 环境温度低
    {DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM, ID1_ENV_ALARM, ID2_ALM_EHHIGH, 1},        // 环境湿度高
    {DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM, ID1_ENV_ALARM, ID2_ALM_EHLOW, 1},        // 环境湿度低
    {DCMU_ALM_ID_FUMES_SENSOR_ALARM, ID1_ENV_ALARM, ID2_ALM_SMOG, 1},        // 烟雾告警
    {DCMU_ALM_ID_FLOOD_SENSOR_ALARM, ID1_ENV_ALARM, ID2_ALM_FLOOD, 1},        // 水淹告警
    {DCMU_ALM_ID_DOORMAT_SENSOR_ALARM, ID1_ENV_ALARM, ID2_ALM_DOORMAG, 1},    // 门磁告警
    {DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM, ID1_ENV_ALARM, ID2_ALM_ETSENSOR, 1},    // 环境温度失效告警
    {DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM, ID1_ENV_ALARM, ID2_ALM_EHSENSOR, 1},    // 环境湿度失效告警

    {DCMU_ALM_ID_INPUT_RELAY1_ALARM, ID1_DCMU_ALARM, ID2_ALM_INRELAY, 1},    // 输入干结点1告警
    {DCMU_ALM_ID_INPUT_RELAY2_ALARM, ID1_DCMU_ALARM, ID2_ALM_INRELAY, 1},    // 输入干结点2告警
    {DCMU_ALM_ID_INPUT_RELAY3_ALARM, ID1_DCMU_ALARM, ID2_ALM_INRELAY, 1},    // 输入干结点3告警
    {DCMU_ALM_ID_INPUT_RELAY4_ALARM, ID1_DCMU_ALARM, ID2_ALM_INRELAY, 1},    // 输入干结点4告警
};
 
 /*
 const MIB_AnalogDataNode AnalogData_MIBTable[] =
 {
 
     // 注意:实时模拟量数据结构要以这个表格的顺序来定义
     // DCMU模拟量
     //{ID1_DCMU_ANALOG, ID2_GENERAL, 6, TYPE_INT16S, 5, 1 },                // 所有DCMU模拟量ID2总数为6
     {ID1_DCMU_ANALOG, ID2_AN_LOADVOLT, 1, TYPE_INT16S, 2, 1 },            // 负载电压
     {ID1_DCMU_ANALOG, ID2_AN_LOADSUMCURR, 1, TYPE_INT16S, 2, 1 },        // 负载总电流
     {ID1_DCMU_ANALOG, ID2_AN_LOADCURR, LOAD_NUM, TYPE_INT16S, 2, 1},    //分路负载电流
     {ID1_DCMU_ANALOG, ID2_AN_BATTVOLT, BATT_NUM, TYPE_INT16S, 2, 1 },    // 电池电压
     {ID1_DCMU_ANALOG, ID2_AN_BATTCURR, BATT_NUM, TYPE_INT16S, 2, 1},    // 电池电流
     {ID1_DCMU_ANALOG, ID2_AN_BATTTEMP, BATT_NUM, TYPE_INT8S, 1, 0 },    // 电池温度
     {ID1_DCMU_ANALOG, ID2_AN_TOTALPOWER, 1, TYPE_FP32, 4, 2 },    // 总电量//tcf
     {ID1_DCMU_ANALOG, ID2_AN_LOADPOWER, LOAD_NUM, TYPE_FP32, 4, 2 },    // 负载电量//tcf

     // ENV模拟量
     //{ID1_ENV_ANALOG, ID2_GENERAL, 2, TYPE_INT8S, 2, 0 },                // 所有ENV模拟量ID2总数为2
     {ID1_ENV_ANALOG, ID2_AN_ENVTEMP, 1, TYPE_INT8S, 1, 0 },                // 环境温度
     {ID1_ENV_ANALOG, ID2_AN_ENVHUM, 1, TYPE_INT8S, 1, 0 },                // 环境湿度

     {0xFF,0xFF, 0, TYPE_INT8U, 1, 0 },                // IDNumber为0表明数组结束
 };

 const MIB_DigitalDataNode StatusData_MIBTable[]=
 {
     // 注意:实时状态量数据结构要以这个表格的顺序来定义
     //{ID1_DCMU_STATUS, ID2_GENERAL, 1 },                    // ID1 = 0x01时ID2总数是2
     {ID1_DCMU_STATUS, ID2_DG_INRLYSTATE, IN_RELAY_NUM },    // 输入干节点X

     {0xFF, 0xFF, 0 },                // IDNumber为0表明数组结束

 };

 const MIB_CtrlDataNode CtrlData_MIBTable[]=
 {
     // 注意:实时状态量数据结构要以这个表格的顺序来定义
     //{ID1_DCMU_CTRL, ID2_GENERAL, 1},
     {ID1_DCMU_CTRL, ID2_CTL_RESET, 1},

     {0xFF, 0xFF, 0},                // IDNumber为0表明数组结束
 };

 // 操作记录ID MIB表
 const MIB_HisOperIDNode CTRL_MIBTable[]=
 {
     {ID1_DCMU_SETSTATUS, ID2_PR_TIME,1 },    // 系统时间设置

     {ID1_DCMU_CTRL, ID2_CTL_RLYRESET, IN_RELAY_NUM },    // 输出干节点恢复
     {ID1_DCMU_CTRL, ID2_CTL_RLYACT, IN_RELAY_NUM },    // 输出干节点动作
     {ID1_DCMU_CTRL, ID2_CTL_LDRUNPARA,1 },    // 恢复默认运行参数
     {ID1_DCMU_CTRL, ID2_CTL_LDCFGPARA,1 },    // 恢复默认配置参数
     {ID1_DCMU_CTRL, ID2_CTL_DOWMPROG,1 },    // 下载程序
     {ID1_DCMU_CTRL, ID2_CTL_DOWNZK,1 },    // 下载字库
     {ID1_DCMU_CTRL, ID2_CTL_RESET,1 },    // 系统复位
     {0xFF, 0xFF, 0 },    // IDNumber为0表明数组结束
 };
 
 T_FactoryInfo g_tFactInfo = 
 {
     { "ZTE Corporation" },    // 厂家名称
     { "ZXDU88 S402 DCMU" },    // SM名称
     { 1, 23, 00 , 01},                // SM软件版本V1.10(中文)
     2022,05, 11,                 // SM软件发布日期:2016,08, 11,
     {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,    // 预留字节
     0x00, 0x00, 0x00}
 };
 */
 
 /***************************************************************************
 * 函数名称：GetAlarmNode
 * 被 调 用：
 * 输入参数：无
 * 返 回 值：ID1下后台可访问的ID2数量
 * 功能描述：根据ucID1和ucID2查找参数在结构体中的序号
 ***************************************************************************/
 // const MIB_AlarmDataNode * GetAlarmNode( INT8U ucAlarmSn )
 // {
 //     if( ucAlarmSn >= ALARM_CLASS_NUM )
 //     {
 //         return (const MIB_AlarmDataNode *)NULL;
 //     }
 //     else
 //     {
 //         return &Alarm_MIBTable[ucAlarmSn];
 //     }
 // }

 /* Started by AICoder, pid:gc72fk0664k916c142ff08bfb1be344609184fde */
/***************************************************************************
* 函数名称：GetParaNode
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
const MIBTable_ParaNode* GetParaNode(unsigned char ucID1, unsigned char ucID2)
{
    unsigned short i = 0;

    while ( Para_MIBTable[i].ucIDNumber )
    {

        if (Para_MIBTable[i].ucID2 == ucID2 && Para_MIBTable[i].ucID1 == ucID1)
        {
            return &Para_MIBTable[i];
        }

        i++;
    }

    return (const MIBTable_ParaNode *)NULL;
}

const MIBTable_ParaNode* GetInrelayAlmParaNode(unsigned char ucID1, unsigned char ucID2, unsigned char idx)
{
    unsigned short i = 0;

    while ( Para_MIBTable[i].ucIDNumber )
    {

        if (Para_MIBTable[i].ucID2 == ucID2 && Para_MIBTable[i].ucID1 == ucID1)
        {
            return &Para_MIBTable[i+idx];
        }

        i++;
    }

    return (const MIBTable_ParaNode *)NULL;
}

/***************************************************************************
* 函数名称：GetParaNodeByParaId
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
const MIBTable_ParaNode* GetParaNodeByParaId(unsigned short wParaOffsetId)
{
    unsigned short i = 0;

    while ( Para_MIBTable[i].ucIDNumber )
    {
        //if ( Para_MIBTable[i].ucID2 )
        {
            if (Para_MIBTable[i].wParaOffsetId == wParaOffsetId)
            {
                return &Para_MIBTable[i];
            }
        }
        i++;
    }

    return (const MIBTable_ParaNode *)NULL;
}

/***************************************************************************
* 函数名称：GetParaNodeByParaIdNoOffset
* 调    用：
* 被 调 用：
* 输入参数：wParaId:参数ID,不带offset
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
Static const MIBTable_ParaNode* GetParaNodeByParaIdNoOffset(unsigned short wParaId)
{
    unsigned short i = 0 , j = 0;
    unsigned short wParaId_no_index = 0;
    while ( Para_MIBTable[i].ucIDNumber )
    {
        for (j = 0; j < Para_MIBTable[i].ucIDNumber; j++)
        {
            if (wParaId == (Para_MIBTable[i].wParaId + j))
            {
                wParaId_no_index = Para_MIBTable[i].wParaId;
                break;
            }
        }
        
        if (Para_MIBTable[i].wParaId == wParaId_no_index && (Para_MIBTable[i].ucID1 == ID1_DCMU_PARA || Para_MIBTable[i].ucID1 == ID1_ENV_PARA))
        {
            return &Para_MIBTable[i];
        }
        i++;
    }

    return (const MIBTable_ParaNode *)NULL;
}

/***************************************************************************
* 函数名称：GetAlmNodeByParaIdNoOffset
* 调    用：
* 被 调 用：
* 输入参数：wParaId:参数ID,不带offset; ucType:参数类型
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
Static const MIBTable_ParaNode* GetAlmNodeByParaIdNoOffset(unsigned short wParaId, unsigned char ucType)
{
    unsigned short i = 0;
    if (ucType == 0x04)
    {
        wParaId = GET_ALM_PARA_BY_ALM_CODE(wParaId, 0x0000);
    }
    else if (ucType == 0x05)
    {
        wParaId = GET_ALM_PARA_BY_ALM_CODE(wParaId, 0x4000);
    }

    while ( Para_MIBTable[i].ucIDNumber )
    {
        {
            if (Para_MIBTable[i].wParaId == wParaId && (Para_MIBTable[i].ucID1 == ID1_DCMU_ALARM || Para_MIBTable[i].ucID1 == ID1_ENV_ALARM ||
                Para_MIBTable[i].ucID1 == ID1_DCMU_OUTRLY || Para_MIBTable[i].ucID1 == ID1_ENV_OUTRLY))
            {
                return &Para_MIBTable[i];
            }
        }
        i++;
    }

    return (const MIBTable_ParaNode *)NULL;
}

/***************************************************************************
* 函数名称：GetParaNodeByType
* 调    用：
* 被 调 用：
* 输入参数：wParaId:参数ID,不带offset; ucType:参数类型
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
const MIBTable_ParaNode *GetParaNodeByType(unsigned short wParaId, unsigned char ucType)
{
    const MIBTable_ParaNode *ptParaNode = NULL;

    switch(ucType)
    {
        case 0x02:  // 参数
            ptParaNode = GetParaNodeByParaIdNoOffset(wParaId);
            break;
        case 0x04:  // 告警级别
        case 0x05:  // 告警干接点            
            ptParaNode = GetAlmNodeByParaIdNoOffset(wParaId, ucType);
            break;
        default:
            break;
    }

    return ptParaNode;
}

/***************************************************************************
* 函数名称：GetParaOffset
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取参数在MIB库中的偏移量(也就是参数在参数结构体中的偏移量)
***************************************************************************/
unsigned short GetParaOffset(unsigned char ucID1, unsigned char ucID2, unsigned char ucIDIndex)
{
    unsigned short wMIBOffset = 0;
    unsigned short i = 0;

    while (Para_MIBTable[i].ucIDNumber)
    {
        //if ( Para_MIBTable[i].ucID2 )
        {
            if ( Para_MIBTable[i].ucID2 == ucID2 && Para_MIBTable[i].ucID1 == ucID1 )
            {
                if ( ucIDIndex!= 0xff )
                {
                    wMIBOffset += (unsigned short)Para_MIBTable[i].ucDataLen * ucIDIndex;
                }
                return wMIBOffset;
            }
            else
            {
                wMIBOffset += (unsigned short)Para_MIBTable[i].ucDataLen * Para_MIBTable[i].ucIDNumber;
            }
        }
        i++;
    }

    return 0;
}

unsigned short GetInRelayAlmParaOffset(unsigned char ucID1, unsigned char ucID2, unsigned char ucIDIndex, unsigned char ucinrelayIdx)
{
    unsigned short wMIBOffset = 0;
    unsigned short i = 0;

    while (Para_MIBTable[i].ucIDNumber)
    {
        //if ( Para_MIBTable[i].ucID2 )
        {
            if ( Para_MIBTable[i].ucID2 == ucID2 && Para_MIBTable[i].ucID1 == ucID1 )
            {
                if ( ucIDIndex!= 0xff )
                {
                    wMIBOffset += (unsigned short)Para_MIBTable[i].ucDataLen * ucIDIndex;
                    wMIBOffset += ucinrelayIdx * Para_MIBTable[i].ucDataLen;
                }
                return wMIBOffset;
            }
            else
            {
                wMIBOffset += (unsigned short)Para_MIBTable[i].ucDataLen * Para_MIBTable[i].ucIDNumber;
            }
        }
        i++;
    }

    return 0;
}

/***************************************************************************
* 函数名称：GetParaZkSn
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取参数在结构体中的序号
***************************************************************************/
unsigned short GetParaZkSn(unsigned char ucID1, unsigned char ucID2)
{
    unsigned short i = 0;

    while (Para_MIBTable[i].ucIDNumber)
    {
        //跳过各告警级别和输出干接点设置行
        if (Para_MIBTable[i].ucID1 == ID1_DCMU_ALARM || Para_MIBTable[i].ucID1 == ID1_ENV_ALARM
            || Para_MIBTable[i].ucID1 == ID1_DCMU_OUTRLY || Para_MIBTable[i].ucID1 == ID1_ENV_OUTRLY)
        {
            i++;
            continue;
        }

        if (Para_MIBTable[i].ucID1 == ucID1 && Para_MIBTable[i].ucID2 == ucID2)
        {
            return i;
        }
        /*if ( Para_MIBTable[i].ucID2 )
        {
            j++;
        }*/
        i++;
    }

    return 0;
}

/***************************************************************************
* 函数名称：GetAlmClassZk
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取告警字库位置
***************************************************************************/
unsigned char GetAlmClassZk(unsigned char ucID1, unsigned char ucID2, unsigned char ucType)
{
    unsigned char i = 0, wBaseAlmAdd = 0, wBaseRlyAdd = 0;

    while (Para_MIBTable[i].ucIDNumber)
    {
        if (Para_MIBTable[i].ucID1 == ID1_DCMU_ALARM && Para_MIBTable[i].ucID2 == ID2_ALM_COMMON)
        {
            wBaseAlmAdd = i;
        }
        else if (Para_MIBTable[i].ucID1 == ID1_DCMU_OUTRLY && Para_MIBTable[i].ucID2 == ID2_ALM_COMMON)
        {
            wBaseRlyAdd = i;
        }
        else if (Para_MIBTable[i].ucID1 == ucID1 && Para_MIBTable[i].ucID2 == ucID2)
        {
            if (ucType == ID1_ALARM)
                return (i - wBaseAlmAdd);
            else
                return (i - wBaseRlyAdd);
        }
        i++;
    }
    return 0;
}

unsigned char init_mib_table_para_from_dic(void)
{
    int i = 0;
    scope_t scope_v = {0};
    unsigned short para_id_offset = 0;
    unsigned char para_raw_type = 0;
    unsigned char para_precision = 0;
    unsigned short multiple = 0;

    for (i = 0; i < sizeof(Para_MIBTable) / sizeof(MIBTable_ParaNode); i++)
    {
        para_id_offset = Para_MIBTable[i].wParaOffsetId;
        scope_v = get_para_scope_by_para_id_offset(para_id_offset);
        para_raw_type = GET_SYS_PARA_DATA_TYPE(para_id_offset);
        para_precision = get_precision(para_raw_type, para_id_offset);
        Para_MIBTable[i].scPrecision = para_precision;
        multiple = round(pow(10, para_precision));

        switch (para_raw_type)
        {
            case TYPE_STRING:
                break;
            case TYPE_INT16S:
                Para_MIBTable[i].iMaxVal = scope_v.max.ss_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.ss_val * multiple;
                break;
            case TYPE_INT16U:
                Para_MIBTable[i].iMaxVal = scope_v.max.us_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.us_val * multiple;
                break;
            case TYPE_INT8S:
                Para_MIBTable[i].iMaxVal = scope_v.max.sc_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.sc_val * multiple;
                break;
            case TYPE_INT8U:
                Para_MIBTable[i].iMaxVal = scope_v.max.uc_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.uc_val * multiple;
                break;
            case TYPE_FLOAT:
                Para_MIBTable[i].iMaxVal = scope_v.max.f_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.f_val * multiple;
                break;
            default:
                break;
        }
        // rt_kprintf("MIB[%d],id:0x%x, precision:%d, min:%d, max:%d\n",i,para_id_offset, para_precision, Para_MIBTable[i].iMinVal, Para_MIBTable[i].iMaxVal);
    }
    return SUCCESSFUL;
}
/* Ended by AICoder, pid:gc72fk0664k916c142ff08bfb1be344609184fde */

/**
 * @description: 通过参数类型获取排除参数类型的参数ID列表
 * @param {unsigned char} para_type - 要排除的参数类型
 * @param {unsigned short*} out_id_list - 输出参数ID列表缓冲区
 * @param {int} max_num - 缓冲区最大容量
 * @return {int} 失败返回FAILURE(-1)，成功返回实际匹配的参数个数
 */
int get_exclude_para_ids_by_type(unsigned char para_type, unsigned short* out_id_list, int max_num) {
    unsigned short i = 0;
	unsigned short j = 0;
    unsigned short count = 0;

    // 参数有效性检查
    if (out_id_list == NULL || max_num <= 0) {
        return FAILURE;
    }

    // 遍历参数表（假设Para_MIBTable以ucIDNumber=0作为结束标志）
    for (i = 0; i < sizeof(Para_MIBTable)/sizeof(MIBTable_ParaNode) - 1; i++) {
        // 类型不匹配时记录ID
        if (Para_MIBTable[i].ucParaType != para_type) {
            // 检查缓冲区容量
            if (count >= (unsigned short)max_num) {
                return FAILURE; // 缓冲区溢出
            }
			if (Para_MIBTable[i].ucID1 == ID1_DCMU_ALARM || Para_MIBTable[i].ucID1 == ID1_ENV_ALARM || Para_MIBTable[i].ucID1 == ID1_DCMU_OUTRLY || Para_MIBTable[i].ucID1 == ID1_ENV_OUTRLY) {
				continue; 
			}
			for (j = 0; j < Para_MIBTable[i].ucIDNumber; j++) {
				out_id_list[count++] = Para_MIBTable[i].wParaId + j;
			} 
        }
    }
    return count;
}

int get_alarm_sn(unsigned short alarm_id, alarm_map_t** out_map) {
    int i = 0;
    for (i = 0; i < sizeof(alarm_map)/sizeof(alarm_map[0]); i++) {
        if (alarm_map[i].alarm_id == alarm_id) {
            *out_map = &alarm_map[i];
            return i; // 返回数组索引作为序列号
        }
    }
    return -1;
}

const MIB_AlarmDataNode * GetAlarmNode(unsigned char ucAlarmSn)
{
    if (ucAlarmSn >= sizeof(alarm_map) / sizeof(alarm_map[0])) {
        return NULL;
    }

    static MIB_AlarmDataNode alarm_data_node; // 使用静态变量
    rt_memset_s(&alarm_data_node, sizeof(alarm_data_node), 0, sizeof(alarm_data_node));

    alarm_data_node.ucID1 = alarm_map[ucAlarmSn].id1;
    alarm_data_node.ucID2 = alarm_map[ucAlarmSn].id2;
    alarm_data_node.ucIDNumber = alarm_map[ucAlarmSn].number;

    return &alarm_data_node;
}
